<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <RootNamespace>HotPreview.DevTools</RootNamespace>
    <AssemblyName>HotPreview.DevTools</AssemblyName>
    <PackAsTool>true</PackAsTool>
    <ToolCommandName>hotpreview</ToolCommandName>
  </PropertyGroup>

  <PropertyGroup>
    <Title>HotPreview DevTools</Title>
    <PackageDescription>A .NET global tool for launching HotPreview DevTools - a visual development environment for UI component previews</PackageDescription>
    <PackageTags>hotpreview;preview;ui;components;devtools;maui;wpf;uno;tool</PackageTags>
  </PropertyGroup>

  <!-- Include DevTools files directly in the project -->
  <ItemGroup>
    <Content Include="../../../bin/HotPreview.DevToolsApp/Debug/net9.0-desktop/**/*"
             PackagePath="tools/app/%(RecursiveDir)%(Filename)%(Extension)"
             Pack="true" />
  </ItemGroup>

  <!-- Build dependency without reference - ensures DevToolsApp builds first in solution builds -->
  <ItemGroup>
    <MSBuildProjectReference Include="../HotPreview.DevToolsApp/HotPreview.DevToolsApp.csproj">
      <AdditionalProperties>TargetFramework=net9.0-desktop</AdditionalProperties>
    </MSBuildProjectReference>
  </ItemGroup>

</Project>
